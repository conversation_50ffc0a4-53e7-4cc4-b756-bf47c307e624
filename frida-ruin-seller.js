/**
 * <PERSON>ida Ruin Seller - Standalone automated ruin selling script for Dominations
 *
 * This script automatically discovers and processes GoodyHut instances with sellable ruins,
 * calling the SellRuins() method (Token 0x6002B93, Address RVA "0x209DE3C") to clear completed instances.
 *
 * Usage: frida -U -l frida-ruin-seller.js com.nexonm.dominations.adk
 *
 * Note: This is the JavaScript version for jshook compatibility
 */
// Frida script initialization
console.log("🗑️ Frida Ruin Seller v1.0 - Starting initialization...");
class FridaRuinSeller {
    constructor() {
        this.isConnectionHealthy = true;
        this.lastConnectionCheck = 0;
        this.validInstances = [];
        this.currentBatchIndex = 0;
        this.batchSize = 50;
        this.isInitialized = false;
        this.stats = {
            totalInstances: 0,
            validGoodyHuts: 0,
            completedInstances: 0,
            ruinSellAttempts: 0,
            successfulSells: 0,
            failedSells: 0,
            accessViolations: 0,
            methodNotFound: 0,
            connectionErrors: 0,
            retryAttempts: 0,
            startTime: Date.now()
        };
    }
    /**
     * Check connection health and attempt recovery if needed
     */
    checkConnectionHealth() {
        const now = Date.now();
        // Only check every 5 seconds to avoid overhead
        if (now - this.lastConnectionCheck < 5000) {
            return this.isConnectionHealthy;
        }
        this.lastConnectionCheck = now;
        try {
            // Try to access Il2Cpp domain to verify connection
            if (!Il2Cpp.domain || !this.assemblyImage) {
                this.isConnectionHealthy = false;
                console.log("⚠️ Connection health check failed - Il2Cpp domain or assembly not accessible");
                return false;
            }
            // Try to access the EntityController class instead of Object (which may not exist in Assembly-CSharp)
            if (!this.entityControllerClass) {
                this.isConnectionHealthy = false;
                console.log("⚠️ Connection health check failed - EntityController class not accessible");
                return false;
            }
            // Try to get instances to verify the connection is working
            const testInstances = Il2Cpp.gc.choose(this.entityControllerClass);
            if (!testInstances) {
                this.isConnectionHealthy = false;
                console.log("⚠️ Connection health check failed - Cannot access entity instances");
                return false;
            }
            this.isConnectionHealthy = true;
            return true;
        }
        catch (error) {
            this.isConnectionHealthy = false;
            this.stats.connectionErrors++;
            console.log(`⚠️ Connection health check failed: ${error}`);
            return false;
        }
    }
    /**
     * Attempt to recover connection and reinitialize
     */
    async attemptConnectionRecovery() {
        console.log("🔄 Attempting connection recovery...");
        try {
            // Wait a bit before attempting recovery
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Try to reinitialize
            const recovered = await this.initialize();
            if (recovered) {
                this.isConnectionHealthy = true;
                console.log("✅ Connection recovery successful");
                return true;
            }
            else {
                console.log("❌ Connection recovery failed");
                return false;
            }
        }
        catch (error) {
            console.log(`❌ Connection recovery error: ${error}`);
            return false;
        }
    }
    /**
     * Discover available classes in the Assembly-CSharp image
     */
    discoverClasses() {
        try {
            console.log("🔍 Discovering available classes...");
            const classes = [];
            const image = this.assemblyImage;
            // Try to enumerate classes (this depends on frida-il2cpp-bridge implementation)
            if (image && image.classes) {
                for (const cls of image.classes) {
                    if (cls.name) {
                        classes.push(cls.name);
                    }
                }
            }
            // Filter for potential entity/controller classes
            const entityClasses = classes.filter(name => name.toLowerCase().includes('entity') ||
                name.toLowerCase().includes('controller') ||
                name.toLowerCase().includes('goody') ||
                name.toLowerCase().includes('building') ||
                name.toLowerCase().includes('structure'));
            console.log(`📋 Found ${classes.length} total classes, ${entityClasses.length} potential entity classes`);
            if (entityClasses.length > 0) {
                console.log("🎯 Potential entity classes:");
                entityClasses.slice(0, 10).forEach(name => console.log(`   - ${name}`));
                if (entityClasses.length > 10) {
                    console.log(`   ... and ${entityClasses.length - 10} more`);
                }
            }
            return entityClasses;
        }
        catch (error) {
            console.log(`❌ Class discovery failed: ${error}`);
            return [];
        }
    }
    /**
     * Wait for the game to fully load and entities to be available
     */
    async waitForGameToLoad() {
        console.log("⏳ Waiting for game to fully load...");
        const maxWaitTime = 120000; // 2 minutes maximum wait
        const checkInterval = 5000; // Check every 5 seconds
        const startTime = Date.now();
        while (Date.now() - startTime < maxWaitTime) {
            try {
                if (this.entityControllerClass) {
                    const instances = Il2Cpp.gc.choose(this.entityControllerClass);
                    console.log(`🔍 Checking entity count: ${instances.length} EntityController instances found`);
                    if (instances.length > 100) { // Wait for a reasonable number of entities
                        console.log(`✅ Game appears to be loaded with ${instances.length} entities`);
                        return true;
                    }
                }
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                console.log(`⏳ Still waiting for game to load... (${elapsed}s elapsed)`);
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
            catch (error) {
                console.log(`⚠️ Error while waiting for game load: ${error}`);
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
        }
        console.log("❌ Timeout waiting for game to load");
        return false;
    }
    /**
     * Initialize Il2Cpp domain and get required classes with discovery
     */
    async initialize() {
        try {
            console.log("🔧 Initializing Il2Cpp domain...");
            // Get Assembly-CSharp image using frida-il2cpp-bridge
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                return false;
            }
            // Discover available classes first for debugging
            this.discoverClasses();
            // Try multiple potential class names for entity controllers
            const potentialClassNames = [
                "EntityController",
                "Entity",
                "BaseEntity",
                "GameEntity",
                "BuildingController",
                "StructureController",
                "GoodyHutController",
                "CollectibleController"
            ];
            // First, find the EntityController class (even if it has no instances yet)
            for (const className of potentialClassNames) {
                try {
                    this.entityControllerClass = this.assemblyImage.class(className);
                    if (this.entityControllerClass) {
                        console.log(`✅ Found entity class: ${className}`);
                        // Don't require instances yet - just find the class
                        const testInstances = Il2Cpp.gc.choose(this.entityControllerClass);
                        console.log(`🔍 ${className} has ${testInstances.length} instances`);
                        // If we found the class, break and wait for game to load
                        if (className === "EntityController") {
                            break;
                        }
                    }
                }
                catch (error) {
                    console.log(`⚠️ Failed to get class ${className}: ${error}`);
                }
            }
            if (!this.entityControllerClass) {
                console.log("❌ Could not find EntityController class");
                return false;
            }
            // Now wait for the game to fully load and populate entities
            const gameLoaded = await this.waitForGameToLoad();
            if (!gameLoaded) {
                console.log("❌ Game did not load within timeout period");
                return false;
            }
            console.log("✅ Initialization completed - game is loaded and ready");
            return true;
        }
        catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }
    /**
     * Enhanced instance validation with memory address checking
     */
    isValidInstance(instance) {
        try {
            if (!instance)
                return false;
            // Check if instance has a valid handle
            if (!instance.handle || instance.handle.isNull())
                return false;
            // Check if instance is accessible (try to read handle address)
            const handleAddr = instance.handle.toString();
            if (handleAddr === "0x0" || handleAddr === "null")
                return false;
            // Try to access the instance's class to verify it's valid
            if (!instance.class || !instance.class.name)
                return false;
            // Additional memory validation - try to read a small amount from the handle
            try {
                instance.handle.readU8();
                return true;
            }
            catch (memError) {
                return false;
            }
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Safe method invocation with enhanced validation and error handling
     */
    safeInvoke(instance, methodName, ...args) {
        try {
            // Enhanced instance validation
            if (!this.isValidInstance(instance)) {
                this.stats.accessViolations++;
                return { error: "Invalid or inaccessible instance", value: null };
            }
            // Try to get the method
            const method = instance.method(methodName);
            if (!method) {
                this.stats.methodNotFound++;
                return { error: `Method ${methodName} not found`, value: null };
            }
            let result;
            try {
                // First try with provided arguments
                if (args.length > 0) {
                    result = method.invoke(...args);
                }
                else {
                    result = method.invoke();
                }
            }
            catch (paramError) {
                const paramErrorMsg = String(paramError);
                // Handle connection errors
                if (paramErrorMsg.includes("device") || paramErrorMsg.includes("connection") ||
                    paramErrorMsg.includes("lost") || paramErrorMsg.includes("disconnected")) {
                    this.stats.connectionErrors++;
                    return { error: `Connection error: ${paramErrorMsg}`, value: null };
                }
                // Handle access violations more specifically
                if (paramErrorMsg.includes("access violation") || paramErrorMsg.includes("0x0") ||
                    paramErrorMsg.includes("invalid memory") || paramErrorMsg.includes("segmentation")) {
                    this.stats.accessViolations++;
                    return { error: `Access violation: ${paramErrorMsg}`, value: null };
                }
                // Handle "bad argument count" errors with parameter discovery
                if (paramErrorMsg.includes("bad argument count")) {
                    // Try common parameter patterns
                    const parameterAttempts = [
                        [], // No parameters
                        [true], // Boolean parameter
                        [false], // Boolean parameter (opposite)
                        [0], // Integer parameter
                        [1], // Integer parameter
                        [null], // Null parameter
                        [true, 0], // Boolean + integer
                        [false, 0], // Boolean + integer
                    ];
                    for (const params of parameterAttempts) {
                        try {
                            result = method.invoke(...params);
                            // Success - log working parameters for debugging
                            if (params.length > 0) {
                                console.log(`✅ ${methodName} succeeded with parameters: [${params.join(', ')}]`);
                            }
                            break;
                        }
                        catch (attemptError) {
                            continue; // Try next parameter combination
                        }
                    }
                    // If all attempts failed, return method-specific safe defaults
                    if (result === undefined) {
                        if (methodName === "CanCollect" || methodName.includes("Can")) {
                            return { error: null, value: false };
                        }
                        else if (methodName === "IsJobComplete" || methodName.includes("Is")) {
                            return { error: null, value: false };
                        }
                        else if (methodName.includes("Get") && methodName.includes("Amount")) {
                            return { error: null, value: 0 };
                        }
                        else if (methodName.includes("Get") && methodName.includes("Type")) {
                            return { error: null, value: "UNKNOWN" };
                        }
                        else {
                            return { error: `Parameter error: ${paramErrorMsg}`, value: null };
                        }
                    }
                }
                else {
                    throw paramError; // Re-throw non-parameter errors
                }
            }
            return { error: null, value: result };
        }
        catch (error) {
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }
    /**
     * Validate if an EntityController instance has a GoodyHut with sellable ruins
     */
    validateInstance(instance, index) {
        const validation = {
            isValid: false,
            hasGoodyHut: false,
            isCompleted: false,
            hasRuins: false,
            canSell: false,
            state: "UNKNOWN",
            rewardType: "UNKNOWN",
            rewardAmount: null
        };
        try {
            // First, validate the instance itself
            if (!this.isValidInstance(instance)) {
                validation.error = `Invalid instance at index ${index}`;
                return validation;
            }
            // Try multiple field names for GoodyHut component
            const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
            let goodyHutInstance = null;
            for (const fieldName of goodyHutFieldNames) {
                try {
                    const goodyHutField = instance.field(fieldName);
                    if (goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0") {
                        goodyHutInstance = goodyHutField.value;
                        validation.hasGoodyHut = true;
                        break;
                    }
                }
                catch (fieldError) {
                    // Continue trying other field names
                    continue;
                }
            }
            if (!goodyHutInstance) {
                validation.error = "No accessible GoodyHut component found";
                return validation;
            }
            // Validate the GoodyHut instance itself
            if (!this.isValidInstance(goodyHutInstance)) {
                validation.error = "GoodyHut instance is invalid";
                return validation;
            }
            // Check if job is complete
            const isCompleteResult = this.safeInvoke(goodyHutInstance, "IsJobComplete");
            if (isCompleteResult.error) {
                validation.error = `IsJobComplete failed: ${isCompleteResult.error}`;
                return validation;
            }
            validation.isCompleted = isCompleteResult.value === true;
            // Check if can collect (indicates completed state)
            const canCollectResult = this.safeInvoke(goodyHutInstance, "CanCollect");
            if (!canCollectResult.error && canCollectResult.value === true) {
                validation.state = "COMPLETED_AWAITING";
            }
            else if (validation.isCompleted) {
                validation.state = "COMPLETED";
            }
            else {
                validation.state = "COLLECTING";
            }
            // Get reward information with multiple method attempts
            const rewardTypeMethods = [
                "GetRewardType", "GetReward", "GetResourceType", "GetCollectibleType",
                "GetLootType", "GetDropType", "GetItemType", "GetRewardResourceType"
            ];
            const rewardAmountMethods = [
                "GetRewardAmount", "GetAmount", "GetResourceAmount", "GetCollectibleAmount",
                "GetLootAmount", "GetDropAmount", "GetItemAmount", "GetRewardResourceAmount"
            ];
            // Try multiple methods for reward type
            for (const methodName of rewardTypeMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value !== null && result.value !== undefined) {
                    validation.rewardType = result.value.toString();
                    break;
                }
            }
            // Try multiple methods for reward amount
            for (const methodName of rewardAmountMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value !== null && result.value !== undefined) {
                    validation.rewardAmount = Number(result.value) || 0;
                    break;
                }
            }
            // If still unknown, try to get basic info from the instance
            if (validation.rewardType === "UNKNOWN") {
                const nameResult = this.safeInvoke(goodyHutInstance, "GetName");
                if (!nameResult.error && nameResult.value) {
                    validation.rewardType = nameResult.value.toString();
                }
            }
            // Check for sellable ruins (multiple methods to try)
            const ruinCheckMethods = ["HasRuins", "HasDebris", "CanSell", "CanClear"];
            for (const methodName of ruinCheckMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value === true) {
                    validation.hasRuins = true;
                    validation.canSell = true;
                    break;
                }
            }
            // Instance is valid if it has a GoodyHut and is completed with potential ruins
            validation.isValid = validation.hasGoodyHut &&
                (validation.isCompleted || validation.state === "COMPLETED_AWAITING");
            return validation;
        }
        catch (error) {
            validation.error = `Validation error: ${error}`;
            return validation;
        }
    }
    /**
     * Execute SellRuins method on a validated instance with retry logic
     */
    async executeSellRuins(instance, validation, index) {
        const maxRetries = 3;
        let retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                console.log(`🗑️ [${index}] Attempting to sell ruins (attempt ${retryCount + 1}/${maxRetries})...`);
                // Re-validate instance before each attempt
                if (!this.isValidInstance(instance)) {
                    console.log(`❌ [${index}] Instance became invalid during processing`);
                    return false;
                }
                // Try multiple field names for GoodyHut component
                const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
                let goodyHutInstance = null;
                for (const fieldName of goodyHutFieldNames) {
                    try {
                        const goodyHutField = instance.field(fieldName);
                        if (goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0") {
                            goodyHutInstance = goodyHutField.value;
                            break;
                        }
                    }
                    catch (fieldError) {
                        continue;
                    }
                }
                if (!goodyHutInstance || !this.isValidInstance(goodyHutInstance)) {
                    console.log(`❌ [${index}] GoodyHut component not accessible or invalid`);
                    return false;
                }
                // Focus on the correct SellRuins method (Token 0x6002B93, confirmed in decompiled code)
                const sellMethods = [
                    "SellRuins" // This is the confirmed correct method from decompiled GoodyHutHelper
                ];
                // Add method enumeration for debugging only on first few entities
                if (retryCount === 0 && this.stats.ruinSellAttempts < 3) {
                    this.enumerateAvailableMethods(goodyHutInstance, index);
                }
                for (const methodName of sellMethods) {
                    const sellResult = this.safeInvoke(goodyHutInstance, methodName);
                    if (!sellResult.error) {
                        console.log(`✅ [${index}] Successfully executed ${methodName}`);
                        // Fast-track processing for speed optimization
                        console.log(`🎉 [${index}] SellRuins executed successfully - using fast verification`);
                        // Minimal post-processing for speed
                        const updateResult = this.safeInvoke(goodyHutInstance, "Update");
                        const resetResult = this.safeInvoke(goodyHutInstance, "Reset");
                        if (!updateResult.error || !resetResult.error) {
                            console.log(`✅ [${index}] Post-processing completed`);
                        }
                        // Quick verification - just check for state change
                        await new Promise(resolve => setTimeout(resolve, 100));
                        const quickValidation = this.validateInstance(instance, index);
                        if (quickValidation.state !== validation.state || quickValidation.state.includes("AWAITING")) {
                            console.log(`🎉 [${index}] State changed: ${validation.state} → ${quickValidation.state} - Entity processed`);
                            return true;
                        }
                        else {
                            console.log(`⚠️ [${index}] Quick verification shows no state change, trying full verification...`);
                            // Fall back to full verification only if quick check fails
                            const verificationResult = await this.verifyEntityRemoval(instance, goodyHutInstance, validation, index);
                            if (verificationResult.success) {
                                console.log(`🎉 [${index}] Entity successfully removed from game world`);
                                return true;
                            }
                            else {
                                console.log(`⚠️ [${index}] Full verification failed: ${verificationResult.reason}`);
                            }
                        }
                    }
                    else if (sellResult.error.includes("Access violation") || sellResult.error.includes("Connection error")) {
                        // These errors warrant a retry
                        console.log(`⚠️ [${index}] ${methodName} failed with recoverable error: ${sellResult.error}`);
                        break; // Break inner loop to retry
                    }
                    else if (!sellResult.error.includes("not found")) {
                        console.log(`⚠️ [${index}] ${methodName} failed: ${sellResult.error}`);
                    }
                }
                // If primary sell methods didn't fully remove the entity, try cleanup operations
                console.log(`🔧 [${index}] Attempting additional cleanup operations...`);
                const cleanupSuccess = await this.performCleanupOperations(instance, goodyHutInstance, index);
                if (cleanupSuccess) {
                    return true;
                }
                // If we get here, all methods failed - increment retry count
                retryCount++;
                this.stats.retryAttempts++;
                if (retryCount < maxRetries) {
                    console.log(`🔄 [${index}] Retrying after delay (${retryCount}/${maxRetries})...`);
                    // Wait before retry with exponential backoff
                    await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
                }
            }
            catch (error) {
                console.log(`❌ [${index}] Sell execution error: ${error}`);
                retryCount++;
                this.stats.retryAttempts++;
                if (retryCount < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
                }
            }
        }
        console.log(`❌ [${index}] All retry attempts failed`);
        return false;
    }
    /**
     * Perform post-SellRuins processing to trigger state machine updates and cleanup
     */
    async performPostSellRuinsProcessing(instance, goodyHutInstance, index) {
        try {
            console.log(`🔧 [${index}] Performing post-SellRuins processing...`);
            let processedSuccessfully = false;
            // Step 1: Try to access and update the state machine (m_stateMachine field at offset 0x18)
            try {
                const stateMachineField = goodyHutInstance.field("m_stateMachine");
                if (stateMachineField && stateMachineField.value && this.isValidInstance(stateMachineField.value)) {
                    const stateMachine = stateMachineField.value;
                    console.log(`🔧 [${index}] Found state machine, attempting to trigger state update...`);
                    // Try to call Update methods on the state machine
                    const updateMethods = ["Update", "FixedUpdate", "ProcessState", "TriggerTransition"];
                    for (const methodName of updateMethods) {
                        const result = this.safeInvoke(stateMachine, methodName);
                        if (!result.error) {
                            console.log(`✅ [${index}] Successfully called ${methodName} on state machine`);
                            processedSuccessfully = true;
                        }
                    }
                }
            }
            catch (error) {
                console.log(`⚠️ [${index}] State machine processing failed: ${error}`);
            }
            // Step 2: Call essential update methods only (streamlined for speed)
            const essentialMethods = ["Update", "FixedUpdate", "Reset"];
            for (const methodName of essentialMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error) {
                    console.log(`✅ [${index}] Successfully called ${methodName} on GoodyHutHelper`);
                    processedSuccessfully = true;
                }
            }
            // Step 3: Minimal wait for processing to complete (optimized for speed)
            if (processedSuccessfully) {
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            return processedSuccessfully;
        }
        catch (error) {
            console.log(`❌ [${index}] Post-SellRuins processing error: ${error}`);
            return false;
        }
    }
    /**
     * Enumerate available methods on a GoodyHutHelper instance for debugging
     */
    enumerateAvailableMethods(goodyHutInstance, index) {
        try {
            console.log(`🔍 [${index}] Enumerating available methods on GoodyHutHelper instance:`);
            if (!this.isValidInstance(goodyHutInstance)) {
                console.log(`⚠️ [${index}] Cannot enumerate methods - invalid instance`);
                return;
            }
            // Try to get the class information
            const instanceClass = goodyHutInstance.class;
            if (!instanceClass) {
                console.log(`⚠️ [${index}] Cannot get class information`);
                return;
            }
            console.log(`📋 [${index}] Class name: ${instanceClass.name}`);
            // Try to enumerate methods if available
            if (instanceClass.methods && instanceClass.methods.length > 0) {
                console.log(`📋 [${index}] Available methods (${instanceClass.methods.length} total):`);
                const relevantMethods = instanceClass.methods.filter((method) => method.name && (method.name.toLowerCase().includes('sell') ||
                    method.name.toLowerCase().includes('clear') ||
                    method.name.toLowerCase().includes('ruin') ||
                    method.name.toLowerCase().includes('debris') ||
                    method.name.toLowerCase().includes('update') ||
                    method.name.toLowerCase().includes('reset')));
                if (relevantMethods.length > 0) {
                    relevantMethods.forEach((method) => {
                        console.log(`   - ${method.name}`);
                    });
                }
                else {
                    console.log(`   No relevant methods found (sell/clear/ruin/debris/update/reset)`);
                    // Show first 10 methods for debugging
                    instanceClass.methods.slice(0, 10).forEach((method) => {
                        console.log(`   - ${method.name} (general)`);
                    });
                }
            }
            else {
                console.log(`⚠️ [${index}] No methods available or methods not accessible`);
            }
        }
        catch (error) {
            console.log(`❌ [${index}] Method enumeration failed: ${error}`);
        }
    }
    /**
     * Verify that an entity has been successfully removed from the game world
     */
    async verifyEntityRemoval(instance, goodyHutInstance, originalValidation, index) {
        try {
            // Wait for the game to process the removal (optimized timing)
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Check if the instance is still valid and accessible
            if (!this.isValidInstance(instance)) {
                return { success: true, reason: "Instance no longer accessible (likely removed)" };
            }
            // Re-validate the instance to see if its state changed
            const postValidation = this.validateInstance(instance, index);
            // Check if the instance no longer has a GoodyHut component
            if (!postValidation.hasGoodyHut) {
                return { success: true, reason: "GoodyHut component removed" };
            }
            // Check if the GoodyHut instance itself is no longer valid
            if (!this.isValidInstance(goodyHutInstance)) {
                return { success: true, reason: "GoodyHut instance no longer accessible" };
            }
            // Check if the state changed significantly
            if (postValidation.state !== originalValidation.state) {
                console.log(`📊 [${index}] State changed: ${originalValidation.state} → ${postValidation.state}`);
                // If it's no longer completed or no longer has ruins, consider it successful
                if (!postValidation.isCompleted || !postValidation.hasRuins) {
                    return { success: true, reason: `State changed to ${postValidation.state}` };
                }
                // Check for specific state transitions that indicate removal
                if (postValidation.state.includes("CLEANING") || postValidation.state.includes("REMOVED") ||
                    postValidation.state.includes("CLEARED") || postValidation.state.includes("EMPTY")) {
                    return { success: true, reason: `State indicates removal: ${postValidation.state}` };
                }
            }
            // Additional checks for entity removal indicators
            // Check if reward information is no longer available (indicates processing)
            if (originalValidation.rewardAmount !== null && postValidation.rewardAmount === null) {
                return { success: true, reason: "Reward information cleared (indicates processing)" };
            }
            // Check if the entity is no longer in a "completed" state
            if (originalValidation.isCompleted && !postValidation.isCompleted) {
                return { success: true, reason: "Entity no longer in completed state" };
            }
            // Try to check if the entity is still discoverable in a fresh entity search
            const currentEntityCount = this.getCurrentEntityCount();
            if (currentEntityCount >= 0 && currentEntityCount < this.stats.totalInstances) {
                // Entity count has decreased, which is a good sign
                return { success: true, reason: `Entity count decreased (${this.stats.totalInstances} → ${currentEntityCount})` };
            }
            // If we get here, the entity appears to still exist in the same state
            return { success: false, reason: `Entity still exists: ${postValidation.state} (${postValidation.rewardType}: ${postValidation.rewardAmount})` };
        }
        catch (error) {
            // If we can't access the instance, it might have been removed
            return { success: true, reason: `Verification error (likely removed): ${error}` };
        }
    }
    /**
     * Perform additional cleanup operations to ensure entity removal
     */
    async performCleanupOperations(instance, goodyHutInstance, index) {
        try {
            console.log(`🔧 [${index}] Performing cleanup operations...`);
            // Try to set cleanup flags (based on user's memory about GoodyHutHelperConfig)
            this.setCleanupFlags(goodyHutInstance, index);
            // Try validation/refresh methods
            const validationMethods = [
                "Validate", "Refresh", "Update", "ProcessCleanup",
                "TriggerCleanup", "ForceUpdate", "Invalidate", "Reset"
            ];
            let cleanupSuccess = false;
            for (const methodName of validationMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error) {
                    console.log(`✅ [${index}] Successfully executed cleanup method: ${methodName}`);
                    cleanupSuccess = true;
                }
            }
            // Try calling cleanup on the parent instance as well
            const parentCleanupMethods = ["ProcessCleanup", "TriggerValidation", "UpdateState", "Refresh"];
            for (const methodName of parentCleanupMethods) {
                const result = this.safeInvoke(instance, methodName);
                if (!result.error) {
                    console.log(`✅ [${index}] Successfully executed parent cleanup method: ${methodName}`);
                    cleanupSuccess = true;
                }
            }
            if (cleanupSuccess) {
                // Wait for cleanup to process
                await new Promise(resolve => setTimeout(resolve, 1500));
                // Verify the cleanup worked
                const verificationResult = await this.verifyEntityRemoval(instance, goodyHutInstance, { state: "CLEANUP_ATTEMPTED" }, index);
                return verificationResult.success;
            }
            return false;
        }
        catch (error) {
            console.log(`❌ [${index}] Cleanup operations error: ${error}`);
            return false;
        }
    }
    /**
     * Set cleanup flags on GoodyHutHelperConfig (based on user's automation preferences)
     */
    setCleanupFlags(goodyHutInstance, index) {
        try {
            // Try to access GoodyHutHelperConfig or similar configuration objects
            const configFieldNames = ["m_config", "config", "_config", "m_helperConfig", "helperConfig"];
            for (const fieldName of configFieldNames) {
                try {
                    const configField = goodyHutInstance.field(fieldName);
                    if (configField && configField.value && this.isValidInstance(configField.value)) {
                        const configInstance = configField.value;
                        // Try to set cleanup flag at offset 0x30 (as mentioned in user's memory)
                        try {
                            const cleanupField = configInstance.field("cleanUp");
                            if (cleanupField) {
                                cleanupField.value = true;
                                console.log(`✅ [${index}] Set cleanUp flag via field access`);
                                return true;
                            }
                        }
                        catch (fieldError) {
                            // Try direct memory access at offset 0x30
                            try {
                                configInstance.handle.add(0x30).writeU8(1);
                                console.log(`✅ [${index}] Set cleanUp flag via memory offset 0x30`);
                                return true;
                            }
                            catch (memError) {
                                // Continue to next config field
                            }
                        }
                    }
                }
                catch (error) {
                    continue;
                }
            }
            // Try setting cleanup flags on the goodyHutInstance itself
            const cleanupFlagNames = ["cleanUp", "m_cleanUp", "_cleanUp", "shouldCleanup", "needsCleanup"];
            for (const flagName of cleanupFlagNames) {
                try {
                    const flagField = goodyHutInstance.field(flagName);
                    if (flagField) {
                        flagField.value = true;
                        console.log(`✅ [${index}] Set ${flagName} flag on GoodyHut instance`);
                        return true;
                    }
                }
                catch (error) {
                    continue;
                }
            }
            return false;
        }
        catch (error) {
            console.log(`⚠️ [${index}] Failed to set cleanup flags: ${error}`);
            return false;
        }
    }
    /**
     * Discover all EntityController instances using multiple strategies
     */
    discoverInstances() {
        try {
            console.log("🔍 Discovering EntityController instances...");
            let entityInstances = Il2Cpp.gc.choose(this.entityControllerClass);
            if (!entityInstances || entityInstances.length === 0) {
                console.log("⚠️ No instances found with primary class, trying alternative discovery...");
                // Try discovering instances of related classes
                const alternativeClasses = [
                    "GoodyHut",
                    "Building",
                    "Structure",
                    "Collectible",
                    "GameObject"
                ];
                for (const altClassName of alternativeClasses) {
                    try {
                        const altClass = this.assemblyImage.class(altClassName);
                        if (altClass) {
                            const altInstances = Il2Cpp.gc.choose(altClass);
                            console.log(`🔍 Found ${altInstances.length} ${altClassName} instances`);
                            if (altInstances.length > 0) {
                                // Filter instances that might have GoodyHut components
                                const filteredInstances = altInstances.filter((instance) => {
                                    try {
                                        const goodyHutField = instance.field("m_goodyHut") ||
                                            instance.field("goodyHut") ||
                                            instance.field("_goodyHut");
                                        return goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0";
                                    }
                                    catch (_a) {
                                        return false;
                                    }
                                });
                                if (filteredInstances.length > 0) {
                                    console.log(`✅ Found ${filteredInstances.length} ${altClassName} instances with GoodyHut components`);
                                    entityInstances = filteredInstances;
                                    break;
                                }
                            }
                        }
                    }
                    catch (error) {
                        console.log(`⚠️ Failed to check ${altClassName}: ${error}`);
                    }
                }
            }
            if (!entityInstances || entityInstances.length === 0) {
                console.log("❌ No suitable instances found with any discovery method");
                return [];
            }
            console.log(`✅ Discovered ${entityInstances.length} instances for processing`);
            this.stats.totalInstances = entityInstances.length;
            return entityInstances;
        }
        catch (error) {
            console.log(`❌ Instance discovery failed: ${error}`);
            return [];
        }
    }
    /**
     * Process a batch of entities in parallel for maximum speed
     */
    async processBatch(batch, batchNumber, totalBatches) {
        console.log(`🚀 Starting parallel processing of batch ${batchNumber}/${totalBatches} (${batch.length} entities)`);
        const results = {
            successful: 0,
            failed: 0,
            total: batch.length
        };
        // Process all entities in the batch simultaneously
        const batchPromises = batch.map(async ({ instance, validation, index }, batchIndex) => {
            try {
                this.stats.ruinSellAttempts++;
                // Get GoodyHut instance
                const goodyHutFieldNames = ["m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"];
                let goodyHutInstance = null;
                for (const fieldName of goodyHutFieldNames) {
                    try {
                        const goodyHutField = instance.field(fieldName);
                        if (goodyHutField && goodyHutField.value && goodyHutField.value.toString() !== "0x0") {
                            goodyHutInstance = goodyHutField.value;
                            break;
                        }
                    }
                    catch (fieldError) {
                        continue;
                    }
                }
                if (!goodyHutInstance || !this.isValidInstance(goodyHutInstance)) {
                    console.log(`❌ [${index}] GoodyHut component not accessible`);
                    return false;
                }
                // Execute SellRuins
                const sellResult = this.safeInvoke(goodyHutInstance, "SellRuins");
                if (sellResult.error) {
                    console.log(`❌ [${index}] SellRuins failed: ${sellResult.error}`);
                    return false;
                }
                // Quick post-processing
                this.safeInvoke(goodyHutInstance, "Update");
                this.safeInvoke(goodyHutInstance, "Reset");
                console.log(`✅ [${index}] Batch ${batchNumber} entity ${batchIndex + 1}/${batch.length} processed`);
                return true;
            }
            catch (error) {
                console.log(`❌ [${index}] Batch processing error: ${error}`);
                return false;
            }
        });
        // Wait for all entities in the batch to complete
        const batchResults = await Promise.all(batchPromises);
        // Count results
        results.successful = batchResults.filter(result => result === true).length;
        results.failed = batchResults.filter(result => result === false).length;
        console.log(`🎯 Batch ${batchNumber} Results: ${results.successful}/${results.total} successful (${((results.successful / results.total) * 100).toFixed(1)}% success rate)`);
        // Wait a moment for game to process all the batch operations
        await new Promise(resolve => setTimeout(resolve, 500));
        return results;
    }
    /**
     * Get current entity count for progress tracking
     */
    getCurrentEntityCount() {
        try {
            const currentInstances = this.discoverInstances();
            return currentInstances.length;
        }
        catch (error) {
            console.log(`⚠️ Failed to get current entity count: ${error}`);
            return -1;
        }
    }
    /**
     * Process all instances for ruin selling with dynamic progress tracking
     */
    async processAllInstances() {
        let initialInstances = this.discoverInstances();
        if (initialInstances.length === 0) {
            return;
        }
        const initialCount = initialInstances.length;
        console.log(`🎯 Starting with ${initialCount} total entities`);
        let processedCount = 0;
        let actuallyRemovedCount = 0;
        let lastEntityCountCheck = initialCount;
        console.log("🔍 Filtering instances for GoodyHut components with sellable ruins...");
        const validInstances = [];
        // First pass: validate all instances
        for (let i = 0; i < initialInstances.length; i++) {
            const instance = initialInstances[i];
            const validation = this.validateInstance(instance, i);
            if (validation.hasGoodyHut) {
                this.stats.validGoodyHuts++;
            }
            if (validation.isCompleted) {
                this.stats.completedInstances++;
            }
            // Include instances that are completed and potentially have ruins
            if (validation.isValid && (validation.hasRuins || validation.isCompleted)) {
                validInstances.push({ instance, validation, index: i });
                console.log(`📋 [${i}] Queued: ${validation.state} (${validation.rewardType}: ${validation.rewardAmount})`);
            }
            // Progress update every 1000 instances
            if ((i + 1) % 1000 === 0) {
                const progress = ((i + 1) / initialInstances.length * 100).toFixed(1);
                console.log(`📊 Validation progress: ${progress}% (${i + 1}/${initialInstances.length})`);
            }
        }
        console.log(`🎯 Found ${validInstances.length} instances ready for ruin selling`);
        console.log(`📊 Summary: ${this.stats.validGoodyHuts} GoodyHuts, ${this.stats.completedInstances} completed`);
        if (validInstances.length === 0) {
            console.log("ℹ️ No instances found with sellable ruins");
            return;
        }
        // Second pass: execute ruin selling in batches
        console.log("🗑️ Starting batch ruin selling operations...");
        const batchSize = 100; // Process 100 entities per batch for maximum speed
        const totalBatches = Math.ceil(validInstances.length / batchSize);
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const batchStart = batchIndex * batchSize;
            const batchEnd = Math.min(batchStart + batchSize, validInstances.length);
            const currentBatch = validInstances.slice(batchStart, batchEnd);
            console.log(`\n🔥 Processing Batch ${batchIndex + 1}/${totalBatches} (${currentBatch.length} entities)`);
            // Check connection health before each batch
            if (!this.checkConnectionHealth()) {
                console.log("⚠️ Connection unhealthy, attempting recovery...");
                const recovered = await this.attemptConnectionRecovery();
                if (!recovered) {
                    console.log("❌ Connection recovery failed, stopping processing");
                    break;
                }
            }
            // Process batch in parallel for maximum speed
            const batchResults = await this.processBatch(currentBatch, batchIndex + 1, totalBatches);
            // Update statistics
            actuallyRemovedCount += batchResults.successful;
            processedCount += batchResults.total;
            this.stats.successfulSells += batchResults.successful;
            this.stats.failedSells += batchResults.failed;
            // Progress update after each batch
            const overallProgress = ((batchEnd) / validInstances.length * 100).toFixed(1);
            console.log(`� Batch ${batchIndex + 1} Complete: ${batchResults.successful}/${batchResults.total} successful`);
            console.log(`📊 Overall Progress: ${overallProgress}% (${batchEnd}/${validInstances.length})`);
            console.log(`📊 Total Stats: ${this.stats.successfulSells} success, ${this.stats.failedSells} failed`);
            // Check actual entity count reduction after each batch
            const currentEntityCount = this.getCurrentEntityCount();
            if (currentEntityCount >= 0) {
                const actualReduction = lastEntityCountCheck - currentEntityCount;
                console.log(`🎯 Entity Count Update: ${currentEntityCount} remaining (reduced by ${actualReduction} this batch)`);
                console.log(`📈 Batch Efficiency: ${batchResults.successful}/${batchResults.total} operations resulted in entity removal`);
                lastEntityCountCheck = currentEntityCount;
            }
            // Small delay between batches to prevent overwhelming the system
            if (batchIndex < totalBatches - 1) {
                console.log(`⏳ Waiting 2 seconds before next batch...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        // Final entity count check
        const finalEntityCount = this.getCurrentEntityCount();
        if (finalEntityCount >= 0) {
            const totalReduction = initialCount - finalEntityCount;
            console.log(`\n🎯 FINAL ENTITY COUNT ANALYSIS:`);
            console.log(`   Initial Entities: ${initialCount}`);
            console.log(`   Final Entities: ${finalEntityCount}`);
            console.log(`   Total Reduction: ${totalReduction}`);
            console.log(`   Removal Rate: ${((totalReduction / initialCount) * 100).toFixed(1)}%`);
            console.log(`   Operations vs Actual Removals: ${this.stats.successfulSells} operations → ${totalReduction} actual removals`);
        }
    }
    /**
     * Generate final statistics report
     */
    generateReport() {
        this.stats.endTime = Date.now();
        this.stats.executionTimeMs = this.stats.endTime - this.stats.startTime;
        console.log("\n" + "=".repeat(60));
        console.log("🗑️ FRIDA RUIN SELLER - EXECUTION REPORT");
        console.log("=".repeat(60));
        console.log(`📊 Instance Discovery:`);
        console.log(`   Total Instances: ${this.stats.totalInstances}`);
        console.log(`   Valid GoodyHuts: ${this.stats.validGoodyHuts}`);
        console.log(`   Completed Instances: ${this.stats.completedInstances}`);
        console.log("");
        console.log(`🗑️ Ruin Selling Operations:`);
        console.log(`   Attempts: ${this.stats.ruinSellAttempts}`);
        console.log(`   Successful: ${this.stats.successfulSells}`);
        console.log(`   Failed: ${this.stats.failedSells}`);
        if (this.stats.ruinSellAttempts > 0) {
            const successRate = (this.stats.successfulSells / this.stats.ruinSellAttempts * 100).toFixed(1);
            console.log(`   Success Rate: ${successRate}%`);
        }
        console.log("");
        console.log(`⚠️ Error Analysis:`);
        console.log(`   Access Violations: ${this.stats.accessViolations}`);
        console.log(`   Method Not Found: ${this.stats.methodNotFound}`);
        console.log(`   Connection Errors: ${this.stats.connectionErrors}`);
        console.log(`   Retry Attempts: ${this.stats.retryAttempts}`);
        console.log("");
        console.log(`⏱️ Performance Metrics:`);
        console.log(`   Execution Time: ${this.stats.executionTimeMs}ms (${(this.stats.executionTimeMs / 1000).toFixed(2)}s)`);
        if (this.stats.ruinSellAttempts > 0 && this.stats.executionTimeMs) {
            const opsPerSecond = (this.stats.ruinSellAttempts / (this.stats.executionTimeMs / 1000)).toFixed(2);
            console.log(`   Operations/Second: ${opsPerSecond}`);
        }
        console.log("=".repeat(60));
        if (this.stats.successfulSells > 0) {
            console.log(`✅ Successfully processed ${this.stats.successfulSells} ruin selling operations!`);
        }
        else {
            console.log(`ℹ️ No ruin selling operations were completed.`);
        }
        console.log("🗑️ Frida Ruin Seller execution completed.");
    }
    /**
     * Initialize and prepare for manual batch processing
     */
    async initialize() {
        try {
            console.log("🗑️ Frida Ruin Seller - Initializing for manual batch processing...");
            // Initialize Il2Cpp domain
            if (!(await this.initializeInternal())) {
                console.log("❌ Initialization failed - aborting execution");
                return;
            }
            // Discover and validate all instances
            await this.discoverAndValidateInstances();
            this.isInitialized = true;
            console.log("✅ Initialization complete! Type 'nextBatch()' to process the next batch.");
        }
        catch (error) {
            console.log(`❌ Fatal error during initialization: ${error}`);
        }
    }
    /**
     * Process the next batch manually
     */
    async nextBatch() {
        if (!this.isInitialized) {
            console.log("❌ Not initialized! Call initialize() first.");
            return;
        }
        if (this.currentBatchIndex * this.batchSize >= this.validInstances.length) {
            console.log("🎉 All batches completed!");
            this.generateReport();
            return;
        }
        const batchStart = this.currentBatchIndex * this.batchSize;
        const batchEnd = Math.min(batchStart + this.batchSize, this.validInstances.length);
        const currentBatch = this.validInstances.slice(batchStart, batchEnd);
        const totalBatches = Math.ceil(this.validInstances.length / this.batchSize);
        console.log(`\n🔥 Processing Batch ${this.currentBatchIndex + 1}/${totalBatches} (${currentBatch.length} entities)`);
        console.log(`📊 Progress: ${((batchEnd / this.validInstances.length) * 100).toFixed(1)}% (${batchEnd}/${this.validInstances.length})`);
        // Check connection health
        if (!this.checkConnectionHealth()) {
            console.log("⚠️ Connection unhealthy, attempting recovery...");
            const recovered = await this.attemptConnectionRecovery();
            if (!recovered) {
                console.log("❌ Connection recovery failed");
                return;
            }
        }
        // Process the batch
        const batchResults = await this.processBatch(currentBatch, this.currentBatchIndex + 1, totalBatches);
        // Update statistics
        this.stats.successfulSells += batchResults.successful;
        this.stats.failedSells += batchResults.failed;
        console.log(`✅ Batch ${this.currentBatchIndex + 1} Complete: ${batchResults.successful}/${batchResults.total} successful`);
        console.log(`📊 Total Stats: ${this.stats.successfulSells} success, ${this.stats.failedSells} failed`);
        // Check entity count
        const currentEntityCount = this.getCurrentEntityCount();
        if (currentEntityCount >= 0) {
            console.log(`🎯 Current Entity Count: ${currentEntityCount} remaining`);
        }
        this.currentBatchIndex++;
        if (this.currentBatchIndex * this.batchSize >= this.validInstances.length) {
            console.log("🎉 All batches completed!");
            this.generateReport();
        }
        else {
            console.log(`\n⏳ Ready for next batch. Type 'nextBatch()' to continue.`);
            console.log(`📋 Remaining: ${Math.ceil((this.validInstances.length - batchEnd) / this.batchSize)} batches`);
        }
    }
    /**
     * Internal initialization method (renamed from original initialize)
     */
    async initializeInternal() {
    }
    perform() { }
}
() => {
    console.log("🚀 Frida Ruin Seller - Il2Cpp bridge context established");
    // Add global error handler for unhandled exceptions
    process.on('uncaughtException', (error) => {
        console.log(`❌ Uncaught exception: ${error}`);
        console.log("🔄 Attempting graceful recovery...");
    });
    // Wait for game to be fully loaded before starting
    setTimeout(async () => {
        let retryCount = 0;
        const maxRetries = 3;
        while (retryCount < maxRetries) {
            try {
                console.log(`🔧 Starting ruin seller execution (attempt ${retryCount + 1}/${maxRetries})...`);
                const ruinSeller = new FridaRuinSeller();
                await ruinSeller.run();
                console.log("✅ Execution completed successfully");
                break; // Success, exit retry loop
            }
            catch (error) {
                const errorMsg = String(error);
                console.log(`❌ Script execution failed: ${errorMsg}`);
                // Check if this is a recoverable error
                if (errorMsg.includes("device") || errorMsg.includes("connection") ||
                    errorMsg.includes("lost") || errorMsg.includes("disconnected")) {
                    console.log("🔄 Connection error detected, attempting recovery...");
                    retryCount++;
                    if (retryCount < maxRetries) {
                        const delay = 500 * retryCount; // Exponential backoff
                        console.log(`⏳ Waiting ${delay}ms before retry...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
                else {
                    // Non-recoverable error, exit
                    console.log("❌ Non-recoverable error, stopping execution");
                    break;
                }
            }
        }
        if (retryCount >= maxRetries) {
            console.log("❌ Maximum retry attempts reached, execution failed");
        }
    }, 10000); // Wait 10 seconds for game to start loading before initializing
};
;
// Export for potential external usage
globalThis.FridaRuinSeller = FridaRuinSeller;
console.log("🗑️ Frida Ruin Seller script loaded successfully - execution will begin shortly...");
