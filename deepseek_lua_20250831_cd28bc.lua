-- Frida Ruin Seller - Standalone automated ruin selling script for Dominations
--
-- This script automatically discovers and processes GoodyHut instances with sellable ruins,
-- calling the SellRuins() method (Token 0x6002B93, Address RVA "0x209DE3C") to clear completed instances.
--
-- Usage: frida -U -l frida-ruin-seller.lua com.nexonm.dominations.adk

-- Frida script initialization
print("🗑️ Frida Ruin Seller v1.0 - Starting initialization...")

-- Type definitions for Il2Cpp interop
local Il2CppObject = {}
Il2CppObject.__index = Il2CppObject

function Il2CppObject:isNull()
    return self.handle == nil or self.handle:isNull()
end

function Il2CppObject:method(name)
    return self.class and self.class.methods and self.class.methods[name] or nil
end

function Il2CppObject:field(name)
    return self.class and self.class.fields and self.class.fields[name] or nil
end

local ProcessingStats = {
    totalInstances = 0,
    validGoodyHuts = 0,
    completedInstances = 0,
    ruinSellAttempts = 0,
    successfulSells = 0,
    failedSells = 0,
    accessViolations = 0,
    methodNotFound = 0,
    connectionErrors = 0,
    retryAttempts = 0,
    startTime = os.time() * 1000
}

local InstanceValidation = {
    isValid = false,
    hasGoodyHut = false,
    isCompleted = false,
    hasRuins = false,
    canSell = false,
    state = "UNKNOWN",
    rewardType = "UNKNOWN",
    rewardAmount = nil,
    error = nil
}

local FridaRuinSeller = {}
FridaRuinSeller.__index = FridaRuinSeller

function FridaRuinSeller.new()
    local self = setmetatable({}, FridaRuinSeller)
    self.stats = {
        totalInstances = 0,
        validGoodyHuts = 0,
        completedInstances = 0,
        ruinSellAttempts = 0,
        successfulSells = 0,
        failedSells = 0,
        accessViolations = 0,
        methodNotFound = 0,
        connectionErrors = 0,
        retryAttempts = 0,
        startTime = os.time() * 1000
    }
    self.assemblyImage = nil
    self.entityControllerClass = nil
    self.isConnectionHealthy = true
    self.lastConnectionCheck = 0
    return self
end

-- Check connection health and attempt recovery if needed
function FridaRuinSeller:checkConnectionHealth()
    local now = os.time() * 1000

    -- Only check every 5 seconds to avoid overhead
    if now - self.lastConnectionCheck < 5000 then
        return self.isConnectionHealthy
    end

    self.lastConnectionCheck = now

    try {
        function()
            -- Try to access Il2Cpp domain to verify connection
            if not Il2Cpp or not Il2Cpp.domain or not self.assemblyImage then
                self.isConnectionHealthy = false
                print("⚠️ Connection health check failed - Il2Cpp domain or assembly not accessible")
                return false
            end

            -- Try to access the EntityController class instead of Object
            if not self.entityControllerClass then
                self.isConnectionHealthy = false
                print("⚠️ Connection health check failed - EntityController class not accessible")
                return false
            end

            -- Try to get instances to verify the connection is working
            local testInstances = Il2Cpp.gc.choose(self.entityControllerClass)
            if not testInstances then
                self.isConnectionHealthy = false
                print("⚠️ Connection health check failed - Cannot access entity instances")
                return false
            end

            self.isConnectionHealthy = true
            return true
        end,
        catch = function(error)
            self.isConnectionHealthy = false
            self.stats.connectionErrors = self.stats.connectionErrors + 1
            print("⚠️ Connection health check failed: " .. tostring(error))
            return false
        end
    }
end

-- Attempt to recover connection and reinitialize
function FridaRuinSeller:attemptConnectionRecovery()
    print("🔄 Attempting connection recovery...")

    try {
        function()
            -- Wait a bit before attempting recovery
            local delay = 2000
            local start = os.time() * 1000
            while os.time() * 1000 - start < delay do
                -- Busy wait
            end

            -- Try to reinitialize
            local recovered = self:initialize()
            if recovered then
                self.isConnectionHealthy = true
                print("✅ Connection recovery successful")
                return true
            else
                print("❌ Connection recovery failed")
                return false
            end
        end,
        catch = function(error)
            print("❌ Connection recovery error: " .. tostring(error))
            return false
        end
    }
end

-- Discover available classes in the Assembly-CSharp image
function FridaRuinSeller:discoverClasses()
    try {
        function()
            print("🔍 Discovering available classes...")

            local classes = {}
            local image = self.assemblyImage

            -- Try to enumerate classes
            if image and image.classes then
                for _, cls in ipairs(image.classes) do
                    if cls.name then
                        table.insert(classes, cls.name)
                    end
                end
            end

            -- Filter for potential entity/controller classes
            local entityClasses = {}
            for _, name in ipairs(classes) do
                local lowerName = name:lower()
                if lowerName:find('entity') or
                   lowerName:find('controller') or
                   lowerName:find('goody') or
                   lowerName:find('building') or
                   lowerName:find('structure') then
                    table.insert(entityClasses, name)
                end
            end

            print("📋 Found " .. #classes .. " total classes, " .. #entityClasses .. " potential entity classes")

            if #entityClasses > 0 then
                print("🎯 Potential entity classes:")
                for i = 1, math.min(10, #entityClasses) do
                    print("   - " .. entityClasses[i])
                end
                if #entityClasses > 10 then
                    print("   ... and " .. (#entityClasses - 10) .. " more")
                end
            end

            return entityClasses
        end,
        catch = function(error)
            print("❌ Class discovery failed: " .. tostring(error))
            return {}
        end
    }
end

-- Initialize Il2Cpp domain and get required classes with discovery
function FridaRuinSeller:initialize()
    try {
        function()
            print("🔧 Initializing Il2Cpp domain...")

            -- Get Assembly-CSharp image
            self.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image
            if not self.assemblyImage then
                print("❌ Failed to get Assembly-CSharp image")
                return false
            end

            -- Discover available classes first for debugging
            self:discoverClasses()

            -- Try multiple potential class names for entity controllers
            local potentialClassNames = {
                "EntityController",
                "Entity",
                "BaseEntity",
                "GameEntity",
                "BuildingController",
                "StructureController",
                "GoodyHutController",
                "CollectibleController"
            }

            for _, className in ipairs(potentialClassNames) do
                try {
                    function()
                        self.entityControllerClass = self.assemblyImage.class(className)
                        if self.entityControllerClass then
                            print("✅ Found entity class: " .. className)

                            -- Test if we can find instances of this class
                            local testInstances = Il2Cpp.gc.choose(self.entityControllerClass)
                            print("🔍 " .. className .. " has " .. #testInstances .. " instances")

                            if #testInstances > 0 then
                                print("✅ Using " .. className .. " as entity class (" .. #testInstances .. " instances found)")
                                return true
                            end
                        end
                        return false
                    end,
                    catch = function(error)
                        print("⚠️ Failed to get class " .. className .. ": " .. tostring(error))
                        return false
                    end
                }
            end

            print("❌ No suitable entity controller class found with instances")
            return false
        end,
        catch = function(error)
            print("❌ Initialization failed: " .. tostring(error))
            return false
        end
    }
end

-- Enhanced instance validation with memory address checking
function FridaRuinSeller:isValidInstance(instance)
    try {
        function()
            if not instance then return false end

            -- Check if instance has a valid handle
            if not instance.handle or instance.handle:isNull() then return false end

            -- Check if instance is accessible (try to read handle address)
            local handleAddr = tostring(instance.handle)
            if handleAddr == "0x0" or handleAddr == "null" then return false end

            -- Try to access the instance's class to verify it's valid
            if not instance.class or not instance.class.name then return false end

            -- Additional memory validation - try to read a small amount from the handle
            try {
                function()
                    instance.handle:readU8()
                    return true
                end,
                catch = function(memError)
                    return false
                end
            }
        end,
        catch = function(error)
            return false
        end
    }
end

-- Safe method invocation with enhanced validation and error handling
function FridaRuinSeller:safeInvoke(instance, methodName, ...)
    try {
        function()
            -- Enhanced instance validation
            if not self:isValidInstance(instance) then
                self.stats.accessViolations = self.stats.accessViolations + 1
                return { error = "Invalid or inaccessible instance", value = nil }
            end

            -- Try to get the method
            local method = instance:method(methodName)
            if not method then
                self.stats.methodNotFound = self.stats.methodNotFound + 1
                return { error = "Method " .. methodName .. " not found", value = nil }
            end
            
            local result = nil
            local args = {...}

            try {
                function()
                    -- First try with provided arguments
                    if #args > 0 then
                        result = method:invoke(table.unpack(args))
                    else
                        result = method:invoke()
                    end
                    return { error = nil, value = result }
                end,
                catch = function(paramError)
                    local paramErrorMsg = tostring(paramError)

                    -- Handle connection errors
                    if paramErrorMsg:find("device") or paramErrorMsg:find("connection") or
                       paramErrorMsg:find("lost") or paramErrorMsg:find("disconnected") then
                        self.stats.connectionErrors = self.stats.connectionErrors + 1
                        return { error = "Connection error: " .. paramErrorMsg, value = nil }
                    end

                    -- Handle access violations more specifically
                    if paramErrorMsg:find("access violation") or paramErrorMsg:find("0x0") or
                       paramErrorMsg:find("invalid memory") or paramErrorMsg:find("segmentation") then
                        self.stats.accessViolations = self.stats.accessViolations + 1
                        return { error = "Access violation: " .. paramErrorMsg, value = nil }
                    end

                    -- Handle "bad argument count" errors with parameter discovery
                    if paramErrorMsg:find("bad argument count") then
                        -- Try common parameter patterns
                        local parameterAttempts = {
                            {}, -- No parameters
                            {true}, -- Boolean parameter
                            {false}, -- Boolean parameter (opposite)
                            {0}, -- Integer parameter
                            {1}, -- Integer parameter
                            {nil}, -- Null parameter
                            {true, 0}, -- Boolean + integer
                            {false, 0}, -- Boolean + integer
                        }

                        for _, params in ipairs(parameterAttempts) do
                            try {
                                function()
                                    result = method:invoke(table.unpack(params))
                                    -- Success - log working parameters for debugging
                                    if #params > 0 then
                                        print("✅ " .. methodName .. " succeeded with parameters: [" .. table.concat(params, ', ') .. "]")
                                    end
                                    return { error = nil, value = result }
                                end,
                                catch = function(attemptError)
                                    -- Try next parameter combination
                                end
                            }
                        end

                        -- If all attempts failed, return method-specific safe defaults
                        if result == nil then
                            if methodName:find("CanCollect") or methodName:find("Can") then
                                return { error = nil, value = false }
                            elseif methodName:find("IsJobComplete") or methodName:find("Is") then
                                return { error = nil, value = false }
                            elseif methodName:find("Get") and methodName:find("Amount") then
                                return { error = nil, value = 0 }
                            elseif methodName:find("Get") and methodName:find("Type") then
                                return { error = nil, value = "UNKNOWN" }
                            else
                                return { error = "Parameter error: " .. paramErrorMsg, value = nil }
                            end
                        end
                    else
                        error(paramError) -- Re-throw non-parameter errors
                    end
                end
            }
        end,
        catch = function(error)
            local errorMsg = tostring(error)
            if errorMsg:find("access violation") or errorMsg:find("0x0") then
                return { error = "Access violation - invalid instance", value = nil }
            end
            return { error = "Method error: " .. errorMsg, value = nil }
        end
    }
end

-- Validate if an EntityController instance has a GoodyHut with sellable ruins
function FridaRuinSeller:validateInstance(instance, index)
    local validation = {
        isValid = false,
        hasGoodyHut = false,
        isCompleted = false,
        hasRuins = false,
        canSell = false,
        state = "UNKNOWN",
        rewardType = "UNKNOWN",
        rewardAmount = nil,
        error = nil
    }

    try {
        function()
            -- First, validate the instance itself
            if not self:isValidInstance(instance) then
                validation.error = "Invalid instance at index " .. index
                return validation
            end

            -- Try multiple field names for GoodyHut component
            local goodyHutFieldNames = {"m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"}
            local goodyHutInstance = nil

            for _, fieldName in ipairs(goodyHutFieldNames) do
                try {
                    function()
                        local goodyHutField = instance:field(fieldName)
                        if goodyHutField and goodyHutField.value and tostring(goodyHutField.value) ~= "0x0" then
                            goodyHutInstance = goodyHutField.value
                            validation.hasGoodyHut = true
                            return true
                        end
                        return false
                    end,
                    catch = function(fieldError)
                        -- Continue trying other field names
                    end
                }
            end

            if not goodyHutInstance then
                validation.error = "No accessible GoodyHut component found"
                return validation
            end

            -- Validate the GoodyHut instance itself
            if not self:isValidInstance(goodyHutInstance) then
                validation.error = "GoodyHut instance is invalid"
                return validation
            end

            -- Check if job is complete
            local isCompleteResult = self:safeInvoke(goodyHutInstance, "IsJobComplete")
            if isCompleteResult.error then
                validation.error = "IsJobComplete failed: " .. isCompleteResult.error
                return validation
            end

            validation.isCompleted = isCompleteResult.value == true

            -- Check if can collect (indicates completed state)
            local canCollectResult = self:safeInvoke(goodyHutInstance, "CanCollect")
            if not canCollectResult.error and canCollectResult.value == true then
                validation.state = "COMPLETED_AWAITING"
            elseif validation.isCompleted then
                validation.state = "COMPLETED"
            else
                validation.state = "COLLECTING"
            end

            -- Get reward information with multiple method attempts
            local rewardTypeMethods = {
                "GetRewardType", "GetReward", "GetResourceType", "GetCollectibleType",
                "GetLootType", "GetDropType", "GetItemType", "GetRewardResourceType"
            }
            local rewardAmountMethods = {
                "GetRewardAmount", "GetAmount", "GetResourceAmount", "GetCollectibleAmount",
                "GetLootAmount", "GetDropAmount", "GetItemAmount", "GetRewardResourceAmount"
            }

            -- Try multiple methods for reward type
            for _, methodName in ipairs(rewardTypeMethods) do
                local result = self:safeInvoke(goodyHutInstance, methodName)
                if not result.error and result.value ~= nil then
                    validation.rewardType = tostring(result.value)
                    break
                end
            end

            -- Try multiple methods for reward amount
            for _, methodName in ipairs(rewardAmountMethods) do
                local result = self:safeInvoke(goodyHutInstance, methodName)
                if not result.error and result.value ~= nil then
                    validation.rewardAmount = tonumber(result.value) or 0
                    break
                end
            end

            -- If still unknown, try to get basic info from the instance
            if validation.rewardType == "UNKNOWN" then
                local nameResult = self:safeInvoke(goodyHutInstance, "GetName")
                if not nameResult.error and nameResult.value then
                    validation.rewardType = tostring(nameResult.value)
                end
            end

            -- Check for sellable ruins (multiple methods to try)
            local ruinCheckMethods = {"HasRuins", "HasDebris", "CanSell", "CanClear"}
            for _, methodName in ipairs(ruinCheckMethods) do
                local result = self:safeInvoke(goodyHutInstance, methodName)
                if not result.error and result.value == true then
                    validation.hasRuins = true
                    validation.canSell = true
                    break
                end
            end

            -- Instance is valid if it has a GoodyHut and is completed with potential ruins
            validation.isValid = validation.hasGoodyHut and 
                                (validation.isCompleted or validation.state == "COMPLETED_AWAITING")

            return validation
        end,
        catch = function(error)
            validation.error = "Validation error: " .. tostring(error)
            return validation
        end
    }
end

-- Execute SellRuins method on a validated instance with retry logic
function FridaRuinSeller:executeSellRuins(instance, validation, index)
    local maxRetries = 3
    local retryCount = 0

    while retryCount < maxRetries do
        try {
            function()
                print("🗑️ [" .. index .. "] Attempting to sell ruins (attempt " .. (retryCount + 1) .. "/" .. maxRetries .. ")...")

                -- Re-validate instance before each attempt
                if not self:isValidInstance(instance) then
                    print("❌ [" .. index .. "] Instance became invalid during processing")
                    return false
                end

                -- Try multiple field names for GoodyHut component
                local goodyHutFieldNames = {"m_goodyHut", "goodyHut", "_goodyHut", "m_GoodyHut"}
                local goodyHutInstance = nil

                for _, fieldName in ipairs(goodyHutFieldNames) do
                    try {
                        function()
                            local goodyHutField = instance:field(fieldName)
                            if goodyHutField and goodyHutField.value and tostring(goodyHutField.value) ~= "0x0" then
                                goodyHutInstance = goodyHutField.value
                                return true
                            end
                            return false
                        end,
                        catch = function(fieldError)
                            -- Continue to next field
                        end
                    }
                end

                if not goodyHutInstance or not self:isValidInstance(goodyHutInstance) then
                    print("❌ [" .. index .. "] GoodyHut component not accessible or invalid")
                    return false
                end

                -- Focus on the correct SellRuins method
                local sellMethods = {"SellRuins"}

                -- Add method enumeration for debugging on first attempt
                if retryCount == 0 then
                    self:enumerateAvailableMethods(goodyHutInstance, index)
                end

                for _, methodName in ipairs(sellMethods) do
                    local sellResult = self:safeInvoke(goodyHutInstance, methodName)

                    if not sellResult.error then
                        print("✅ [" .. index .. "] Successfully executed " .. methodName)

                        -- Perform post-SellRuins processing
                        local postProcessingSuccess = self:performPostSellRuinsProcessing(instance, goodyHutInstance, index)

                        -- Perform comprehensive post-operation verification
                        local verificationResult = self:verifyEntityRemoval(instance, goodyHutInstance, validation, index)
                        if verificationResult.success then
                            print("🎉 [" .. index .. "] Entity successfully removed from game world")
                            return true
                        else
                            print("⚠️ [" .. index .. "] " .. methodName .. " executed but entity not removed: " .. verificationResult.reason)
                            if postProcessingSuccess then
                                print("🔧 [" .. index .. "] Post-processing completed, entity may be marked for removal")
                            end
                            -- Continue to try additional cleanup methods
                        end
                    elseif sellResult.error:find("Access violation") or sellResult.error:find("Connection error") then
                        -- These errors warrant a retry
                        print("⚠️ [" .. index .. "] " .. methodName .. " failed with recoverable error: " .. sellResult.error)
                        break -- Break inner loop to retry
                    elseif not sellResult.error:find("not found") then
                        print("⚠️ [" .. index .. "] " .. methodName .. " failed: " .. sellResult.error)
                    end
                end

                -- If primary sell methods didn't fully remove the entity, try cleanup operations
                print("🔧 [" .. index .. "] Attempting additional cleanup operations...")
                local cleanupSuccess = self:performCleanupOperations(instance, goodyHutInstance, index)
                if cleanupSuccess then
                    return true
                end

                -- If we get here, all methods failed - increment retry count
                retryCount = retryCount + 1
                self.stats.retryAttempts = self.stats.retryAttempts + 1

                if retryCount < maxRetries then
                    print("🔄 [" .. index .. "] Retrying after delay (" .. retryCount .. "/" .. maxRetries .. ")...")
                    -- Wait before retry with exponential backoff
                    local delay = 100 * retryCount
                    local start = os.time() * 1000
                    while os.time() * 1000 - start < delay do
                        -- Busy wait
                    end
                end

                return false
            end,
            catch = function(error)
                print("❌ [" .. index .. "] Sell execution error: " .. tostring(error))
                retryCount = retryCount + 1
                self.stats.retryAttempts = self.stats.retryAttempts + 1

                if retryCount < maxRetries then
                    local delay = 100 * retryCount
                    local start = os.time() * 1000
                    while os.time() * 1000 - start < delay do
                        -- Busy wait
                    end
                end

                return false
            end
        }
    end

    print("❌ [" .. index .. "] All retry attempts failed")
    return false
end

-- Perform post-SellRuins processing to trigger state machine updates and cleanup
function FridaRuinSeller:performPostSellRuinsProcessing(instance, goodyHutInstance, index)
    try {
        function()
            print("🔧 [" .. index .. "] Performing post-SellRuins processing...")
            local processedSuccessfully = false

            -- Step 1: Try to access and update the state machine
            try {
                function()
                    local stateMachineField = goodyHutInstance:field("m_stateMachine")
                    if stateMachineField and stateMachineField.value and self:isValidInstance(stateMachineField.value) then
                        local stateMachine = stateMachineField.value
                        print("🔧 [" .. index .. "] Found state machine, attempting to trigger state update...")

                        -- Try to call Update methods on the state machine
                        local updateMethods = {"Update", "FixedUpdate", "ProcessState", "TriggerTransition"}
                        for _, methodName in ipairs(updateMethods) do
                            local result = self:safeInvoke(stateMachine, methodName)
                            if not result.error then
                                print("✅ [" .. index .. "] Successfully called " .. methodName .. " on state machine")
                                processedSuccessfully = true
                            end
                        end
                    end
                end,
                catch = function(error)
                    print("⚠️ [" .. index .. "] State machine processing failed: " .. tostring(error))
                end
            }

            -- Step 2: Try to call Update/FixedUpdate methods on the GoodyHutHelper instance
            local updateMethods = {"Update", "FixedUpdate", "LateUpdate", "ProcessUpdate"}
            for _, methodName in ipairs(updateMethods) do
                local result = self:safeInvoke(goodyHutInstance, methodName)
                if not result.error then
                    print("✅ [" .. index .. "] Successfully called " .. methodName .. " on GoodyHutHelper")
                    processedSuccessfully = true
                end
            end

            -- Step 3: Try to call Reset or cleanup methods
            local resetMethods = {"Reset", "ResetState", "ClearState", "Initialize"}
            for _, methodName in ipairs(resetMethods) do
                local result = self:safeInvoke(goodyHutInstance, methodName)
                if not result.error then
                    print("✅ [" .. index .. "] Successfully called " .. methodName .. " on GoodyHutHelper")
                    processedSuccessfully = true
                end
            end

            -- Step 4: Try to call Update methods on the parent EntityController
            if self:isValidInstance(instance) then
                local parentUpdateMethods = {"Update", "FixedUpdate", "ProcessUpdate", "Refresh"}
                for _, methodName in ipairs(parentUpdateMethods) do
                    local result = self:safeInvoke(instance, methodName)
                    if not result.error then
                        print("✅ [" .. index .. "] Successfully called " .. methodName .. " on EntityController")
                        processedSuccessfully = true
                    end
                end
            end

            -- Step 5: Wait for processing to complete
            if processedSuccessfully then
                print("⏳ [" .. index .. "] Waiting for post-processing to complete...")
                local delay = 200
                local start = os.time() * 1000
                while os.time() * 1000 - start < delay do
                    -- Busy wait
                end
            end

            return processedSuccessfully
        end,
        catch = function(error)
            print("❌ [" .. index .. "] Post-SellRuins processing error: " .. tostring(error))
            return false
        end
    }
end

-- Enumerate available methods on a GoodyHutHelper instance for debugging
function FridaRuinSeller:enumerateAvailableMethods(goodyHutInstance, index)
    try {
        function()
            print("🔍 [" .. index .. "] Enumerating available methods on GoodyHutHelper instance:")

            if not self:isValidInstance(goodyHutInstance) then
                print("⚠️ [" .. index .. "] Cannot enumerate methods - invalid instance")
                return
            end

            -- Try to get the class information
            local instanceClass = goodyHutInstance.class
            if not instanceClass then
                print("⚠️ [" .. index .. "] Cannot get class information")
                return
            end

            print("📋 [" .. index .. "] Class name: " .. instanceClass.name)

            -- Try to enumerate methods if available
            if instanceClass.methods and #instanceClass.methods > 0 then
                print("📋 [" .. index .. "] Available methods (" .. #instanceClass.methods .. " total):")
                local relevantMethods = {}
                for _, method in ipairs(instanceClass.methods) do
                    if method.name and (
                        method.name:lower():find('sell') or
                        method.name:lower():find('clear') or
                        method.name:lower():find('ruin') or
                        method.name:lower():find('debris') or
                        method.name:lower():find('update') or
                        method.name:lower():find('reset')
                    ) then
                        table.insert(relevantMethods, method)
                    end
                end

                if #relevantMethods > 0 then
                    for _, method in ipairs(relevantMethods) do
                        print("   - " .. method.name)
                    end
                else
                    print("   No relevant methods found (sell/clear/ruin/debris/update/reset)")
                    -- Show first 10 methods for debugging
                    for i = 1, math.min(10, #instanceClass.methods) do
                        print("   - " .. instanceClass.methods[i].name .. " (general)")
                    end
                end
            else
                print("⚠️ [" .. index .. "] No methods available or methods not accessible")
            end
        end,
        catch = function(error)
            print("❌ [" .. index .. "] Method enumeration failed: " .. tostring(error))
        end
    }
end

-- Verify that an entity has been successfully removed from the game world
function FridaRuinSeller:verifyEntityRemoval(instance, goodyHutInstance, originalValidation, index)
    try {
        function()
            -- Wait longer for the game to process the removal
            local delay = 100
            local start = os.time() * 1000
            while os.time() * 1000 - start < delay do
                -- Busy wait
            end

            -- Check if the instance is still valid and accessible
            if not self:isValidInstance(instance) then
                return { success = true, reason = "Instance no longer accessible (likely removed)" }
            end
            
            -- Re-validate the instance to see if its state changed
            local postValidation = self:validateInstance(instance, index)

            -- Check if the instance no longer has a GoodyHut component
            if not postValidation.hasGoodyHut then
                return { success = true, reason = "GoodyHut component removed" }
            end

            -- Check if the GoodyHut instance itself is no longer valid
            if not self:isValidInstance(goodyHutInstance) then
                return { success = true, reason = "GoodyHut instance no longer accessible" }
            end

            -- Check if the state changed significantly
            if postValidation.state ~= originalValidation.state then
                print("📊 [" .. index .. "] State changed: " .. originalValidation.state .. " → " .. postValidation.state)

                -- If it's no longer completed or no longer has ruins, consider it successful
                if not postValidation.isCompleted or not postValidation.hasRuins then
                    return { success = true, reason = "State changed to " .. postValidation.state }
                end

                -- Check for specific state transitions that indicate removal
                if postValidation.state:find("CLEANING") or postValidation.state:find("REMOVED") or
                   postValidation.state:find("CLEARED") or postValidation.state:find("EMPTY") then
                    return { success = true, reason = "State indicates removal: " .. postValidation.state }
                end
            end

            -- Additional checks for entity removal indicators
            -- Check if reward information is no longer available
            if originalValidation.rewardAmount ~= nil and postValidation.rewardAmount == nil then
                return { success = true, reason = "Reward information cleared (indicates processing)" }
            end

            -- Check if the entity is no longer in a "completed" state
            if originalValidation.isCompleted and not postValidation.isCompleted then
                return { success = true, reason = "Entity no longer in completed state" }
            end

            -- Try to check if the entity is still discoverable in a fresh entity search
            local currentEntityCount = self:getCurrentEntityCount()
            if currentEntityCount >= 0 and currentEntityCount < self.stats.totalInstances then
                -- Entity count has decreased, which is a good sign
                return { success = true, reason = "Entity count decreased (" .. self.stats.totalInstances .. " → " .. currentEntityCount .. ")" }
            end

            -- If we get here, the entity appears to still exist in the same state
            return { success = false, reason = "Entity still exists: " .. postValidation.state .. " (" .. postValidation.rewardType .. ": " .. tostring(postValidation.rewardAmount) .. ")" }
        end,
        catch = function(error)
            -- If we can't access the instance, it might have been removed
            return { success = true, reason = "Verification error (likely removed): " .. tostring(error) }
        end
    }
end

-- Perform additional cleanup operations to ensure entity removal
function FridaRuinSeller:performCleanupOperations(instance, goodyHutInstance, index)
    try {
        function()
            print("🔧 [" .. index .. "] Performing cleanup operations...")

            -- Try to set cleanup flags
            self:setCleanupFlags(goodyHutInstance, index)

            -- Try validation/refresh methods
            local validationMethods = {
                "Validate", "Refresh", "Update", "ProcessCleanup",
                "TriggerCleanup", "ForceUpdate", "Invalidate", "Reset"
            }

            local cleanupSuccess = false
            for _, methodName in ipairs(validationMethods) do
                local result = self:safeInvoke(goodyHutInstance, methodName)
                if not result.error then
                    print("✅ [" .. index .. "] Successfully executed cleanup method: " .. methodName)
                    cleanupSuccess = true
                end
            end

            -- Try calling cleanup on the parent instance as well
            local parentCleanupMethods = {"ProcessCleanup", "TriggerValidation", "UpdateState", "Refresh"}
            for _, methodName in ipairs(parentCleanupMethods) do
                local result = self:safeInvoke(instance, methodName)
                if not result.error then
                    print("✅ [" .. index .. "] Successfully executed parent cleanup method: " .. methodName)
                    cleanupSuccess = true
                end
            end

            if cleanupSuccess then
                -- Wait for cleanup to process
                local delay = 1500
                local start = os.time() * 1000
                while os.time() * 1000 - start < delay do
                    -- Busy wait
                end

                -- Verify the cleanup worked
                local verificationResult = self:verifyEntityRemoval(instance, goodyHutInstance,
                    { state = "CLEANUP_ATTEMPTED" }, index)
                return verificationResult.success
            end

            return false
        end,
        catch = function(error)
            print("❌ [" .. index .. "] Cleanup operations error: " .. tostring(error))
            return false
        end
    }
end

-- Set cleanup flags on GoodyHutHelperConfig
function FridaRuinSeller:setCleanupFlags(goodyHutInstance, index)
    try {
        function()
            -- Try to access GoodyHutHelperConfig or similar configuration objects
            local configFieldNames = {"m_config", "config", "_config", "m_helperConfig", "helperConfig"}

            for _, fieldName in ipairs(configFieldNames) do
                try {
                    function()
                        local configField = goodyHutInstance:field(fieldName)
                        if configField and configField.value and self:isValidInstance(configField.value) then
                            local configInstance = configField.value

                            -- Try to set cleanup flag at offset 0x30
                            try {
                                function()
                                    local cleanupField = configInstance:field("cleanUpAfterSell")
                                    if cleanupField then
                                        cleanupField.value = true
                                        print("✅ [" .. index .. "] Set cleanup flag on config")
                                        return true
                                    end
                                    return false
                                },
                                catch = function(fieldError)
                                    -- Try direct memory access if field not found
                                    try {
                                        function()
                                            -- Try to write to common cleanup flag offsets
                                            local configHandle = configInstance.handle
                                            local cleanupOffsets = {0x30, 0x34, 0x38, 0x3C, 0x40, 0x44}

                                            for _, offset in ipairs(cleanupOffsets) do
                                                try {
                                                    function()
                                                        configHandle:add(offset):writeU8(1)
                                                        print("✅ [" .. index .. "] Set cleanup flag at offset 0x" .. string.format("%X", offset))
                                                        return true
                                                    },
                                                    catch = function(memError)
                                                        -- Try next offset
                                                    end
                                                }
                                            end
                                            return false
                                        },
                                        catch = function(memError)
                                            return false
                                        end
                                    }
                                end
                            }
                        end
                        return false
                    },
                    catch = function(fieldError)
                        -- Continue to next field name
                    end
                }
            end

            return false
        },
        catch = function(error)
            print("⚠️ [" .. index .. "] Failed to set cleanup flags: " .. tostring(error))
            return false
        end
    }
end

-- Get current entity count for verification
function FridaRuinSeller:getCurrentEntityCount()
    try {
        function()
            if not self.entityControllerClass then
                return -1
            end

            local instances = Il2Cpp.gc.choose(self.entityControllerClass)
            return #instances
        },
        catch = function(error)
            return -1
        end
    }
end

-- Main processing loop
function FridaRuinSeller:process()
    try {
        function()
            print("🔄 Starting processing loop...")

            -- Get all EntityController instances
            local instances = Il2Cpp.gc.choose(self.entityControllerClass)
            if not instances or #instances == 0 then
                print("ℹ️ No EntityController instances found")
                return
            end

            self.stats.totalInstances = #instances
            print("📊 Found " .. self.stats.totalInstances .. " EntityController instances")

            -- Process each instance
            for i, instance in ipairs(instances) do
                if not self:isConnectionHealthy then
                    print("⚠️ Connection lost, attempting recovery...")
                    if not self:attemptConnectionRecovery() then
                        print("❌ Failed to recover connection, aborting processing")
                        return
                    end
                    -- Restart processing after recovery
                    self:process()
                    return
                end

                -- Validate the instance
                local validation = self:validateInstance(instance, i)

                if validation.error then
                    print("❌ [" .. i .. "] Validation failed: " .. validation.error)
                else
                    print("📋 [" .. i .. "] " .. validation.state .. " - " .. validation.rewardType .. ": " .. tostring(validation.rewardAmount) .. 
                          (validation.hasRuins and " (has ruins)" or " (no ruins)"))

                    if validation.isValid then
                        self.stats.validGoodyHuts = self.stats.validGoodyHuts + 1

                        if validation.isCompleted or validation.state == "COMPLETED_AWAITING" then
                            self.stats.completedInstances = self.stats.completedInstances + 1

                            if validation.hasRuins and validation.canSell then
                                print("🎯 [" .. i .. "] Eligible for ruin selling: " .. validation.state)
                                self.stats.ruinSellAttempts = self.stats.ruinSellAttempts + 1

                                -- Execute SellRuins
                                local success = self:executeSellRuins(instance, validation, i)

                                if success then
                                    self.stats.successfulSells = self.stats.successfulSells + 1
                                    print("✅ [" .. i .. "] Successfully sold ruins!")
                                else
                                    self.stats.failedSells = self.stats.failedSells + 1
                                    print("❌ [" .. i .. "] Failed to sell ruins")
                                end
                            else
                                print("ℹ️ [" .. i .. "] No ruins to sell or cannot sell")
                            end
                        else
                            print("⏳ [" .. i .. "] Not completed yet: " .. validation.state)
                        end
                    else
                        print("ℹ️ [" .. i .. "] Not a valid GoodyHut instance")
                    end
                end

                -- Small delay between instances to avoid overwhelming the game
                local delay = 50
                local start = os.time() * 1000
                while os.time() * 1000 - start < delay do
                    -- Busy wait
                end
            end

            -- Print summary
            self:printStats()
        end,
        catch = function(error)
            print("❌ Processing error: " .. tostring(error))
            self.stats.connectionErrors = self.stats.connectionErrors + 1

            -- Check if this is a connection error that warrants recovery
            local errorMsg = tostring(error)
            if errorMsg:find("device") or errorMsg:find("connection") or
               errorMsg:find("lost") or errorMsg:find("disconnected") then
                print("⚠️ Connection error detected, attempting recovery...")
                if self:attemptConnectionRecovery() then
                    -- Restart processing after successful recovery
                    self:process()
                end
            end
        end
    }
end

-- Print statistics
function FridaRuinSeller:printStats()
    local elapsed = (os.time() * 1000 - self.stats.startTime) / 1000
    print("\n📊 Processing Summary:")
    print("⏱️  Elapsed time: " .. string.format("%.1f", elapsed) .. "s")
    print("📦 Total instances: " .. self.stats.totalInstances)
    print("🏠 Valid GoodyHuts: " .. self.stats.validGoodyHuts)
    print("✅ Completed instances: " .. self.stats.completedInstances)
    print("🗑️  Ruin sell attempts: " .. self.stats.ruinSellAttempts)
    print("🎉 Successful sells: " .. self.stats.successfulSells)
    print("❌ Failed sells: " .. self.stats.failedSells)
    print("⚠️  Access violations: " .. self.stats.accessViolations)
    print("🔍 Method not found: " .. self.stats.methodNotFound)
    print("📡 Connection errors: " .. self.stats.connectionErrors)
    print("🔄 Retry attempts: " .. self.stats.retryAttempts)
end

-- Main execution
local function main()
    print("🚀 Starting Frida Ruin Seller...")

    -- Create and initialize the seller
    local seller = FridaRuinSeller.new()
    local initialized = seller:initialize()

    if not initialized then
        print("❌ Failed to initialize Frida Ruin Seller")
        return
    end

    print("✅ Initialization successful!")

    -- Run the processing loop
    seller:process()

    print("🎉 Frida Ruin Seller completed!")
end

-- Error handling utilities
function try(tryBlock)
    local status, result = pcall(tryBlock.f)
    if not status then
        if tryBlock.catch then
            tryBlock.catch(result)
        else
            error(result)
        end
    end
    return result
end

-- Start the main function
main()